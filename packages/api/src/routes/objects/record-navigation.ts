import type { Session } from "@repo/auth";
import { db } from "@repo/database/server";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";
import { verifyOrganizationMembership } from "../organizations/lib/membership";

export const recordNavigationRouter = new Hono<{
	Variables: { user: Session["user"] };
}>();

// Define the query schema for record navigation
const RecordNavigationQuerySchema = z.object({
	objectType: z.enum(["contact", "property", "company"]),
	currentId: z.string().min(1),
	organizationId: z.string().min(1),
	// Optional filter parameters
	search: z.string().optional(),
	tags: z.string().optional(),
	propertyType: z.string().optional(),
	status: z.string().optional(),
	sort: z.string().optional(),
	// Pagination parameters
	start: z.string().optional(),
	size: z.string().optional(),
	// View context
	viewName: z.string().optional(),
	viewId: z.string().optional(),
});

// Helper function to build context description
const buildContextDescription = (filters: any, viewName?: string) => {
	const contexts: string[] = [];
	
	// Add search context first (most specific)
	if (filters.search && filters.search.trim() !== "") {
		contexts.push(`"${filters.search}"`);
	}
	
	// Add tag context
	if (filters.tags && filters.tags.trim() !== "") {
		const tagArray = filters.tags.split(",").filter((tag: string) => tag.trim() !== "");
		if (tagArray.length === 1) {
			contexts.push(`#${tagArray[0]}`);
		} else if (tagArray.length > 1) {
			contexts.push(`#${tagArray.join(", #")}`);
		}
	}
	
	// Add status context
	if (filters.status && filters.status.trim() !== "") {
		contexts.push(`${filters.status}`);
	}
	
	// Add property type context
	if (filters.propertyType && filters.propertyType.trim() !== "") {
		const typeArray = filters.propertyType.split(",").filter((type: string) => type.trim() !== "");
		if (typeArray.length === 1) {
			contexts.push(`${typeArray[0]}`);
		} else if (typeArray.length > 1) {
			contexts.push(`${typeArray.join(" or ")}`);
		}
	}
	
	// If we have active filters, create a description
	if (contexts.length > 0) {
		// If there's a view name and filters, combine them
		if (viewName && viewName.trim() !== "" && viewName !== "All") {
			return `${viewName} • ${contexts.join(" • ")}`;
		}
		// Just filters without specific view
		return contexts.join(" • ");
	}
	
	// No active filters - just show view name or default
	if (viewName && viewName.trim() !== "") {
		// Clean up common view names
		const cleanViewName = viewName
			.replace(/\+/g, ' ')
			.replace(/View$/, '')
			.trim();
		return cleanViewName || "All";
	}
	
	// Fallback
	return "All";
};

// Get record navigation context
recordNavigationRouter.get(
	"/record-context",
	authMiddleware,
	validator("query", RecordNavigationQuerySchema),
	describeRoute({
		tags: ["Navigation"],
		summary: "Get record navigation context",
		description: "Get navigation context for a record including previous/next IDs and position within filtered results",
		responses: {
			200: {
				description: "Navigation context retrieved successfully",
				content: {
					"application/json": {
						schema: resolver(
							z.object({
								previousId: z.string().nullable(),
								nextId: z.string().nullable(),
								currentIndex: z.number(),
								totalRecords: z.number(),
								filteredRecords: z.number(),
								hasFilters: z.boolean(),
								contextDescription: z.string(),
								viewName: z.string().optional(),
							}),
						),
					},
				},
			},
			400: {
				description: "Invalid parameters",
				content: {
					"application/json": {
						schema: resolver(z.object({ error: z.string() })),
					},
				},
			},
			404: {
				description: "Record not found",
				content: {
					"application/json": {
						schema: resolver(z.object({ error: z.string() })),
					},
				},
			},
		},
	}),
	async (c) => {
		try {
			const user = c.get("user");
			const query = c.req.valid("query");
			const { objectType, currentId, organizationId, start, size, ...filters } = query;

			// Verify organization membership
			await verifyOrganizationMembership(organizationId, user.id);

			// Parse pagination parameters
			const paginationOffset = start ? parseInt(start, 10) : 0;
			const paginationLimit = size ? parseInt(size, 10) : 25;
			const hasPagination = start !== undefined;

			// Build filter conditions from query parameters
			const buildFilterConditions = (queryParams: typeof filters) => {
				const conditions: any = {
					organizationId,
					isDeleted: false,
				};

				// Handle different filter types based on query parameters
				Object.entries(queryParams).forEach(([key, value]) => {
					if (!value || typeof value !== 'string') return;

					// Handle different filter types
					switch (key) {
						case "search":
							// Add search logic based on object type
							if (objectType === "contact") {
								conditions.OR = [
									{ firstName: { contains: value, mode: "insensitive" } },
									{ lastName: { contains: value, mode: "insensitive" } },
									{ email: { path: "$[*].address", string_contains: value } },
								];
							} else if (objectType === "property") {
								conditions.OR = [
									{ name: { contains: value, mode: "insensitive" } },
									{ address: { contains: value, mode: "insensitive" } },
								];
							} else if (objectType === "company") {
								conditions.OR = [
									{ name: { contains: value, mode: "insensitive" } },
								];
							}
							break;
						case "tags":
							// Handle tag filtering
							const tagArray = value.split(",").filter(Boolean);
							if (tagArray.length > 0) {
								conditions.tags = {
									some: {
										tag: {
											name: { in: tagArray }
										}
									}
								};
							}
							break;
						case "propertyType":
							if (objectType === "property") {
								const typeArray = value.split(",").filter(Boolean);
								if (typeArray.length > 0) {
									conditions.propertyType = { in: typeArray };
								}
							}
							break;
						case "status":
							conditions.status = value;
							break;
						// Add more filter cases as needed
					}
				});

				return conditions;
			};

			// Parse sort parameter
			const getSortParam = (sortString?: string) => {
				if (!sortString) return null;
				
				try {
					return JSON.parse(sortString);
				} catch {
					return null;
				}
			};

			const filterConditions = buildFilterConditions(filters);
			const sortParam = getSortParam(query.sort);

			let totalCount: number;
			let filteredRecords: any[];
			let currentIndex: number;
			let previousId: string | null = null;
			let nextId: string | null = null;

			// Handle different object types with proper typing
			switch (objectType) {
				case "contact": {
					// Build contact-specific sort conditions
					const contactSortConditions = sortParam
						? [{ [sortParam.id]: sortParam.desc ? ("desc" as const) : ("asc" as const) }]
						: [{ firstName: "asc" as const }, { lastName: "asc" as const }];

					totalCount = await db.contact.count({
						where: { organizationId, isDeleted: false },
					});

					if (hasPagination) {
						// For paginated context, get the specific page the user was viewing
						const paginatedRecords = await db.contact.findMany({
							where: filterConditions,
							orderBy: contactSortConditions,
							select: { id: true },
							skip: paginationOffset,
							take: paginationLimit,
						});

						// Find the current record within this paginated view
						const indexInPage = paginatedRecords.findIndex((record: any) => record.id === currentId);
						
						if (indexInPage === -1) {
							// Current record not found in this page, fall back to searching all records
							filteredRecords = await db.contact.findMany({
								where: filterConditions,
								orderBy: contactSortConditions,
								select: { id: true },
							});
							currentIndex = filteredRecords.findIndex((record: any) => record.id === currentId);
						} else {
							// Calculate the actual position within the entire filtered dataset
							currentIndex = paginationOffset + indexInPage; // Absolute position (0-based)
							
							// Get previous record (from previous page if needed)
							if (indexInPage > 0) {
								previousId = paginatedRecords[indexInPage - 1].id;
							} else if (paginationOffset > 0) {
								// Get the last record from the previous page
								const prevPageRecord = await db.contact.findMany({
									where: filterConditions,
									orderBy: contactSortConditions,
									select: { id: true },
									skip: paginationOffset - 1,
									take: 1,
								});
								previousId = prevPageRecord[0]?.id || null;
							}

							// Get next record (from next page if needed)
							if (indexInPage < paginatedRecords.length - 1) {
								nextId = paginatedRecords[indexInPage + 1].id;
							} else {
								// Get the first record from the next page
								const nextPageRecord = await db.contact.findMany({
									where: filterConditions,
									orderBy: contactSortConditions,
									select: { id: true },
									skip: paginationOffset + paginationLimit,
									take: 1,
								});
								nextId = nextPageRecord[0]?.id || null;
							}

							// For pagination mode, we'll return the limited page results
							filteredRecords = paginatedRecords;
						}
					} else {
						// Non-paginated context - get all records
						filteredRecords = await db.contact.findMany({
							where: filterConditions,
							orderBy: contactSortConditions,
							select: { id: true },
						});
						currentIndex = filteredRecords.findIndex((record: any) => record.id === currentId);
					}
					
					break;
				}
				case "property": {
					// Build property-specific sort conditions
					const propertySortConditions = sortParam
						? [{ [sortParam.id]: sortParam.desc ? ("desc" as const) : ("asc" as const) }]
						: [{ name: "asc" as const }];

					totalCount = await db.property.count({
						where: { organizationId, isDeleted: false },
					});

					if (hasPagination) {
						// For paginated context, get the specific page the user was viewing
						const paginatedRecords = await db.property.findMany({
							where: filterConditions,
							orderBy: propertySortConditions,
							select: { id: true },
							skip: paginationOffset,
							take: paginationLimit,
						});

						// Find the current record within this paginated view
						const indexInPage = paginatedRecords.findIndex((record: any) => record.id === currentId);
						
						if (indexInPage === -1) {
							// Current record not found in this page, fall back to searching all records
							filteredRecords = await db.property.findMany({
								where: filterConditions,
								orderBy: propertySortConditions,
								select: { id: true },
							});
							currentIndex = filteredRecords.findIndex((record: any) => record.id === currentId);
						} else {
							// Calculate the actual position within the entire filtered dataset
							currentIndex = paginationOffset + indexInPage;
							
							// Get previous/next records with pagination awareness
							if (indexInPage > 0) {
								previousId = paginatedRecords[indexInPage - 1].id;
							} else if (paginationOffset > 0) {
								const prevPageRecord = await db.property.findMany({
									where: filterConditions,
									orderBy: propertySortConditions,
									select: { id: true },
									skip: paginationOffset - 1,
									take: 1,
								});
								previousId = prevPageRecord[0]?.id || null;
							}

							if (indexInPage < paginatedRecords.length - 1) {
								nextId = paginatedRecords[indexInPage + 1].id;
							} else {
								const nextPageRecord = await db.property.findMany({
									where: filterConditions,
									orderBy: propertySortConditions,
									select: { id: true },
									skip: paginationOffset + paginationLimit,
									take: 1,
								});
								nextId = nextPageRecord[0]?.id || null;
							}

							filteredRecords = paginatedRecords;
						}
					} else {
						filteredRecords = await db.property.findMany({
							where: filterConditions,
							orderBy: propertySortConditions,
							select: { id: true },
						});
						currentIndex = filteredRecords.findIndex((record: any) => record.id === currentId);
					}
					break;
				}
				case "company": {
					// Build company-specific sort conditions
					const companySortConditions = sortParam
						? [{ [sortParam.id]: sortParam.desc ? ("desc" as const) : ("asc" as const) }]
						: [{ name: "asc" as const }];

					totalCount = await db.company.count({
						where: { organizationId, isDeleted: false },
					});

					if (hasPagination) {
						// For paginated context, get the specific page the user was viewing
						const paginatedRecords = await db.company.findMany({
							where: filterConditions,
							orderBy: companySortConditions,
							select: { id: true },
							skip: paginationOffset,
							take: paginationLimit,
						});

						// Find the current record within this paginated view
						const indexInPage = paginatedRecords.findIndex((record: any) => record.id === currentId);
						
						if (indexInPage === -1) {
							// Current record not found in this page, fall back to searching all records
							filteredRecords = await db.company.findMany({
								where: filterConditions,
								orderBy: companySortConditions,
								select: { id: true },
							});
							currentIndex = filteredRecords.findIndex((record: any) => record.id === currentId);
						} else {
							// Calculate the actual position within the entire filtered dataset
							currentIndex = paginationOffset + indexInPage;
							
							// Get previous/next records with pagination awareness
							if (indexInPage > 0) {
								previousId = paginatedRecords[indexInPage - 1].id;
							} else if (paginationOffset > 0) {
								const prevPageRecord = await db.company.findMany({
									where: filterConditions,
									orderBy: companySortConditions,
									select: { id: true },
									skip: paginationOffset - 1,
									take: 1,
								});
								previousId = prevPageRecord[0]?.id || null;
							}

							if (indexInPage < paginatedRecords.length - 1) {
								nextId = paginatedRecords[indexInPage + 1].id;
							} else {
								const nextPageRecord = await db.company.findMany({
									where: filterConditions,
									orderBy: companySortConditions,
									select: { id: true },
									skip: paginationOffset + paginationLimit,
									take: 1,
								});
								nextId = nextPageRecord[0]?.id || null;
							}

							filteredRecords = paginatedRecords;
						}
					} else {
						filteredRecords = await db.company.findMany({
							where: filterConditions,
							orderBy: companySortConditions,
							select: { id: true },
						});
						currentIndex = filteredRecords.findIndex((record: any) => record.id === currentId);
					}
					break;
				}
				default:
					return c.json({ error: `Unsupported object type: ${objectType}` }, 400);
			}

			if (currentIndex === -1) {
				return c.json({ error: "Current record not found in filtered results" }, 404);
			}

			// Calculate total filtered count for context
			let totalFilteredCount: number;
			if (hasPagination) {
				// Get total count of filtered records for display purposes
				switch (objectType) {
					case "contact":
						totalFilteredCount = await db.contact.count({
							where: filterConditions,
						});
						break;
					case "property":
						totalFilteredCount = await db.property.count({
							where: filterConditions,
						});
						break;
					case "company":
						totalFilteredCount = await db.company.count({
							where: filterConditions,
						});
						break;
					default:
						totalFilteredCount = 0;
				}
			} else {
				totalFilteredCount = filteredRecords.length;
			}

			// If we don't have previous/next IDs yet (non-paginated case), calculate them
			if (!hasPagination) {
				previousId = currentIndex > 0 ? filteredRecords[currentIndex - 1]?.id : null;
				nextId = currentIndex < filteredRecords.length - 1 ? filteredRecords[currentIndex + 1]?.id : null;
			}

			// Build context description
			const contextDescription = buildContextDescription(filters, query.viewName);

			logger.info(`Record navigation context retrieved for ${objectType}:${currentId} (${currentIndex + 1}/${hasPagination ? totalFilteredCount : filteredRecords.length})`);

			return c.json({
				previousId,
				nextId,
				currentIndex,
				totalRecords: totalCount,
				filteredRecords: hasPagination ? totalFilteredCount : filteredRecords.length,
				hasFilters: (hasPagination ? totalFilteredCount : filteredRecords.length) !== totalCount,
				contextDescription,
				viewName: query.viewName,
			});

		} catch (error) {
			logger.error("Error in record navigation:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	},
);

export type RecordNavigationRouter = typeof recordNavigationRouter; 