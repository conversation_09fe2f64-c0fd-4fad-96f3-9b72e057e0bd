"use client";

import { inDateRange } from "@app/organizations/lib/filterfns";
import { ContactAvatar } from "@shared/components/ContactAvatar";
import { CopyableValue } from "@shared/components/CopyableValue";
import { EmailCell } from "@shared/components/EmailCell";
import { DataTableColumnTags } from "../shared/data-table/data-table-column/data-table-column-tags";
import {
	IconAt,
	IconBuilding,
	IconCalendarClock,
	IconChartColumn,
	IconClock,
	IconClockEdit,
	IconLetterTSmall,
	IconPhone,
	IconProgress,
	IconSignature,
	IconUser,
	IconTag,
} from "@tabler/icons-react";
import type { ColumnDef } from "@tanstack/react-table";
import { Badge, ContactBadge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Checkbox } from "@ui/components/checkbox";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { ArrowUpDown, Mail, Phone } from "lucide-react";
import type { ContactSchema } from "./schema";



export const columns: ColumnDef<ContactSchema>[] = [
	{
		id: "select",
		header: ({ table }) => (
			<Checkbox
				checked={
					table.getIsAllPageRowsSelected() ||
					(table.getIsSomePageRowsSelected() && "indeterminate")
				}
				onCheckedChange={(value) =>
					table.toggleAllPageRowsSelected(!!value)
				}
				aria-label="Select all"
			/>
		),
		cell: ({ row }) => (
			<Checkbox
				checked={row.getIsSelected()}
				onCheckedChange={(value) => row.toggleSelected(!!value)}
				aria-label="Select row"
			/>
		),
		enableSorting: false,
		enableHiding: false,
		enableResizing: false,
		size: 40,
		minSize: 40,
		maxSize: 40,
	},
	{
		id: "name",
		accessorFn: (row) =>
			`${row.firstName || ""} ${row.lastName || ""}`.trim() ||
			"Unnamed Contact",
		header: ({ column }) => (
			<div className="flex items-center gap-1 text-primary">
				<IconSignature className="h-4 w-4" />
				Full Name
			</div>
		),
		cell: ({ row }) => {
			console.log("row", row);
			
			return (
				<div className="flex items-center gap-1">
					<ContactAvatar
						name={row.original.firstName + " " + row.original.lastName}
						avatarUrl={row.original.image}
						className="h-5 w-5"
					/>
					{row.getValue("name")}
				</div>
			)
		},
		size: 250,
		minSize: 150,
		maxSize: 400,
	},
	{
		id: "email",
		accessorFn: (row) => {
			const primaryEmail =
				row.email.find((e) => e.isPrimary) || row.email[0];
			return primaryEmail?.address || "";
		},
		header: ({ column }) => (
			<div className="items-center flex gap-1 text-primary">
				<IconAt className="h-4 w-4" />
				Email
			</div>
		),
		cell: ({ row }) => {
			const email =
				row.original.email.find((e) => e.isPrimary) ||
				row.original.email[0];
			if (!email) return <span className="text-muted-foreground" />;
			
			const contactName = `${row.original.firstName || ""} ${row.original.lastName || ""}`.trim() || "Contact";
			
			// Get all email addresses for this contact
			const allEmails = row.original.email.map(e => e.address);
			
			return (
				<div className="flex items-center gap-2">
					<EmailCell 
						email={email.address}
						recipientName={contactName}
						subject={`Hello ${contactName}`}
						avatarUrl={row.original.image}
						type="contact"
						allEmails={allEmails}
					/>
				</div>
			);
		},
		size: 250,
		minSize: 150,
		maxSize: 400,
	},
	{
		id: "phone",
		accessorFn: (row) => {
			const primaryPhone =
				row.phone.find((p) => p.isPrimary) || row.phone[0];
			return primaryPhone?.number || "";
		},
		header: ({ column }) => (
			<div className="items-center flex gap-1 text-primary">
				<IconPhone className="h-4 w-4" />
				Phone
			</div>
		),
		cell: ({ row }) => {
			const phone =
				row.original.phone.find((p) => p.isPrimary) ||
				row.original.phone[0];
			if (!phone) return <span className="text-muted-foreground" />;
			return <CopyableValue value={phone.number} type="phone" />;
		},
		size: 180,
		minSize: 120,
		maxSize: 300,
	},
	{
		accessorKey: "status",
		header: ({ column }) => (
			<div className="items-center flex gap-1 text-primary">
				<IconProgress className="h-4 w-4" />
				Status
			</div>
		),
		cell: ({ row }) => {
			const status = row.getValue("status") as string | undefined;
			if (!status) return <span className="text-muted-foreground" />;

			const statusColors: Record<string, string> = {
				active: "bg-green-100 text-green-800",
				inactive: "bg-gray-100 text-gray-800",
				lead: "bg-blue-100 text-blue-800",
				client: "bg-purple-100 text-purple-800",
				past_client: "bg-orange-100 text-orange-800",
			};

			const colorClass =
				statusColors[status] || "bg-gray-100 text-gray-800";

			return (
				<Badge className={`capitalize ${colorClass}`}>{status}</Badge>
			);
		},
		size: 120,
		minSize: 80,
		maxSize: 200,
	},
	{
		accessorKey: "persona",
		header: ({ column }) => (
			<div className="items-center flex gap-1 text-primary">
				<IconUser className="h-4 w-4" />
				Persona
			</div>
		),
		cell: ({ row }) => {
			const persona = row.getValue("persona") as string | undefined;
			if (!persona) return <span className="text-muted-foreground" />;

			const personaColors: Record<string, string> = {
				buyer: "bg-emerald-100 text-emerald-800",
				seller: "bg-orange-100 text-orange-800",
				investor: "bg-yellow-100 text-yellow-800",
				agent: "bg-purple-100 text-purple-800",
				lender: "bg-blue-100 text-blue-800",
				other: "bg-gray-100 text-gray-800",
			};

			const colorClass =
				personaColors[persona] || "bg-gray-100 text-gray-800";

			return (
				<Badge className={`capitalize ${colorClass}`}>{persona}</Badge>
			);
		},
		size: 120,
		minSize: 80,
		maxSize: 200,
	},
	{
		accessorKey: "stage",
		header: ({ column }) => (
			<div className="items-center flex gap-1 text-primary">
				<IconChartColumn className="h-4 w-4" />
				Stage
			</div>
		),
		cell: ({ row }) => {
			const stage = row.getValue("stage") as string | undefined;
			if (!stage) return <span className="text-muted-foreground" />;

			const stageColors: Record<string, string> = {
				new: "bg-gray-100 text-gray-800",
				contacted: "bg-blue-100 text-blue-800",
				qualified: "bg-yellow-100 text-yellow-800",
				proposal: "bg-orange-100 text-orange-800",
				closed: "bg-green-100 text-green-800",
			};

			const colorClass =
				stageColors[stage] || "bg-gray-100 text-gray-800";

			return (
				<Badge className={`capitalize ${colorClass}`}>{stage}</Badge>
			);
		},
		size: 120,
		minSize: 80,
		maxSize: 200,
	},
	{
		accessorKey: "title",
		header: ({ column }) => (
			<div className="items-center flex gap-1 text-primary">
				<IconLetterTSmall className="h-4 w-4" />
				Title
			</div>
		),
		cell: ({ row }) => {
			const title = row.getValue("title") as string | undefined;
			return title;
		},
		size: 150,
		minSize: 100,
		maxSize: 250,
	},
	{
		id: "company",
		accessorFn: (row) => row.company?.name || "",
		header: ({ column }) => (
			<div className="items-center flex gap-1 text-primary">
				<IconBuilding className="h-4 w-4" />
				Company
			</div>
		),
		cell: ({ row }) => {
			const company = row.original.company;
			return company?.name;
		},
		size: 180,
		minSize: 120,
		maxSize: 300,
	},
	{
		accessorKey: "updatedAt",
		header: ({ column }) => (
			<div className="items-center flex gap-1 text-primary">
				<IconClockEdit className="h-4 w-4" />
				Updated
			</div>
		),
		cell: ({ row }) => {
			const date = row.getValue("updatedAt") as Date;
			return (
				<div className="text-muted-foreground">
					{format(new Date(date), "MMMM dd, yyyy, h:mmaaa")
						.replace("AM", "am")
						.replace("PM", "pm")}
				</div>
			);
		},
		size: 130,
		minSize: 100,
		maxSize: 200,
	},
	{
		accessorKey: "createdAt",
		header: ({ column }) => (
			<div className="items-center flex gap-1 text-primary">
				<IconCalendarClock className="h-4 w-4" />
				Created
			</div>
		),
		cell: ({ row }) => {
			const date = row.getValue("createdAt") as Date;
			return (
				<div className="text-muted-foreground">
					{format(new Date(date), "MMMM dd, yyyy, h:mmaaa")
						.replace("AM", "am")
						.replace("PM", "pm")}
				</div>
			);
		},
		filterFn: inDateRange,
		size: 130,
		minSize: 100,
		maxSize: 200,
	},
	{
		id: "tags",
		accessorFn: (row) => row.tags || [],
		header: ({ column }) => (
			<div className="items-center flex gap-1 text-primary">
				<IconTag className="h-4 w-4" />
				Tags
			</div>
		),
		// No custom cell renderer - will use InlineCellEditor with fieldType="tags"
		size: 200,
		minSize: 150,
		maxSize: 400,
	},
];
