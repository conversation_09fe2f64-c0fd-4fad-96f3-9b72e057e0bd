"use client";

import React, { useState, useEffect } from "react";
import { fetchRelatedContact, useContact, useRelatedContacts, useUpdateContact, useUpdateRelatedContacts } from "@app/contacts/lib/api";
import PageHeader from "@shared/components/PageHeader";
import ReorderList from "@ui/components/reorder-list";
import GridItemContainer from "@ui/components/grid-item-container";
import ContactDetails from "./sections/ContactDetails";
import ContactSummary from "./sections/ContactSummary";
import ContactMetadata from "./sections/ContactMetadata";
import ConfigurePageModal from "./ConfigurePageModal";
import { leftColItems, rightColItems } from "./sections/columns";
import type { ActiveOrganization } from "@repo/auth";
import RelatedContacts from "./sections/RelatedContacts";
import { TagSelector } from "@app/shared/components/TagSelector";
import { MoreInfo } from "@app/shared/components/MoreInfo";
import { Activities } from "@app/activities";
import { useSession } from "@app/auth/hooks/use-session";
import Portfolio from "@app/contacts/components/sections/Portfolio";
import LinkedContacts from "@app/contacts/components/sections/LinkedContacts";
import ContactNotes from "@app/contacts/components/sections/ContactNotes";
import ContactTasks from "@app/contacts/components/sections/ContactTasks";
import { fetchContactProperties, useContactProperties } from "@shared/hooks/useContactProperties";
import { useLinkedContacts } from "@shared/hooks/useLinkedContacts";
import { IconPlus } from "@tabler/icons-react";
import { useTasks } from "@app/tasks/lib/tasks-provider";
import { createNote } from "@app/notes/lib/api";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { AccessDeniedError, RecordNotFoundError } from "@shared/components/ErrorStates";
import { DotsLoader, Loader } from "@ui/components/loader";

interface ContactPageProps {
	id: string;
	activeOrganization: ActiveOrganization | null;
	isFavorite: boolean;
}

const ContactPage = ({
	id,
	activeOrganization,
	isFavorite,
}: ContactPageProps) => {
	// PRIMARY DATA - Load immediately
	const { data: contact, isLoading: contactLoading, error: contactError } = useContact(id, activeOrganization?.id);
	const { mutateAsync: updateContact } = useUpdateContact(activeOrganization?.id);
	
	// LAZY LOADING STATE - Only load when needed
	const [loadedSections, setLoadedSections] = useState<Set<string>>(new Set());
	const [currentLoadingSection, setCurrentLoadingSection] = useState<string | null>(null);
	
	// CONDITIONAL QUERIES - Only enabled when sections are loaded
	const { data: relatedContacts } = useQuery({
		queryKey: ["related-contacts", id, activeOrganization?.id],
		queryFn: () => (id && activeOrganization?.id ? fetchRelatedContact(id, activeOrganization.id) : Promise.resolve([])),
		enabled: !!id && !!activeOrganization?.id && loadedSections.has('related-contacts'),
		staleTime: 1000 * 30, // 30 seconds
	});
	
	const { data: contactProperties, isLoading: isLoadingProperties, error: propertiesError } = useQuery({
		queryKey: ["contact-properties", id, activeOrganization?.id],
		queryFn: () => fetchContactProperties(id, activeOrganization?.id || ''),
		enabled: !!id && !!activeOrganization?.id && loadedSections.has('portfolio'),
		staleTime: 1000 * 30,
	});
	
	// Use original hook for linked contacts - only fetch when section is loaded
	const shouldLoadLinkedContacts = loadedSections.has('linked-contacts');
	const { data: linkedContacts, isLoading: isLoadingLinkedContacts } = useLinkedContacts(
		shouldLoadLinkedContacts ? id : '', 
		shouldLoadLinkedContacts ? activeOrganization?.id || '' : ''
	);

	const [isConfigureModalOpen, setIsConfigureModalOpen] = useState(false);
	const { user } = useSession();
	const { openCreateTask } = useTasks();
	const queryClient = useQueryClient();

	// Progressive loading stages
	useEffect(() => {
		if (contact?.id) {
			// Stage 1: Load essential sections immediately
			setLoadedSections(prev => new Set([...Array.from(prev), 'contact-details', 'summary', 'metadata']));
			
			// Stage 2: Load secondary sections after a short delay
			const stage2Timer = setTimeout(() => {
				setCurrentLoadingSection('related-contacts');
				setLoadedSections(prev => new Set([...Array.from(prev), 'related-contacts']));
				
				// Load activities after related contacts
				const activitiesTimer = setTimeout(() => {
					setCurrentLoadingSection('activities');
					setLoadedSections(prev => new Set([...Array.from(prev), 'activities']));
					setCurrentLoadingSection(null);
				}, 1000);
				
				return () => clearTimeout(activitiesTimer);
			}, 500);
			
			// Stage 3: Load resource-intensive sections
			const stage3Timer = setTimeout(() => {
				setCurrentLoadingSection('portfolio');
				setLoadedSections(prev => new Set([...Array.from(prev), 'portfolio', 'tags']));
				
				// Load final sections
				const finalTimer = setTimeout(() => {
					setCurrentLoadingSection('linked-contacts');
					setLoadedSections(prev => new Set([...Array.from(prev), 'linked-contacts', 'tasks']));
					setCurrentLoadingSection(null);
				}, 1500);
				
				return () => clearTimeout(finalTimer);
			}, 2000);
			
			return () => {
				clearTimeout(stage2Timer);
				clearTimeout(stage3Timer);
			};
		}
	}, [contact?.id]);

	// Helper function to check if section is loading
	const isSectionLoading = (sectionId: string) => {
		return currentLoadingSection === sectionId || (!loadedSections.has(sectionId) && currentLoadingSection !== null);
	};

	// Handle error states
	if (contactError) {
		const error = contactError as any;
		if (error.message === "ACCESS_DENIED") {
			return <AccessDeniedError message="You don't have access to view this contact" />;
		}
		if (error.message === "CONTACT_NOT_FOUND") {
			return <RecordNotFoundError recordType="contact" />;
		}
	}

	// Handle properties error states (these are less critical, so we'll just log them)
	if (propertiesError) {
		const error = propertiesError as any;
		if (error.message === "ACCESS_DENIED" || error.message === "CONTACT_NOT_FOUND") {
			console.warn("Contact properties access issue:", error.message);
		}
	}

	// Show loading state
	if (contactLoading) {
		return <div className="min-h-screen flex items-center justify-center !text-muted-foreground gap-2">
			<Loader text="Loading" variant="loading-dots" color="muted" />
		</div>;
	}

	const renderLeftColumn = (item: { id: string; title: string }) => {
		if (!activeOrganization) return null;
		
		switch (item.id) {
			case "contact-details":
				return (
					<GridItemContainer item={item}>
						<ContactDetails data={contact as any} organization={activeOrganization} />
					</GridItemContainer>
				);
			case "related-contacts":
				if (!loadedSections.has('related-contacts')) {
					return (
						<GridItemContainer item={item}>
							<div className="p-4 text-center">
								<div className="flex items-center justify-center gap-2">
									<DotsLoader size="sm" />
									<div className="text-sm text-muted-foreground">Loading related contacts...</div>
								</div>
							</div>
						</GridItemContainer>
					);
				}

				const transformedRelatedContacts = relatedContacts?.map(contact => ({
					...contact,
					id: contact._id?.$oid || "", 
				})) || [];

				return (
					<GridItemContainer item={item}>
						<RelatedContacts 
							data={contact as any} 
							organization={activeOrganization} 
							relatedContacts={transformedRelatedContacts}
							loading={!relatedContacts}
						/>
					</GridItemContainer>
				);
			case "more-info":
				return (
					<GridItemContainer item={item}>
						<MoreInfo 
							data={contact as any} 
							objectType="contact" 
							organization={{ id: activeOrganization.id }}
							onUpdate={async (data) => {
								await updateContact({
									...data,
								});
							}}
						/>
					</GridItemContainer>
				);
			case "tags":
				if (!loadedSections.has('tags')) {
					return (
						<GridItemContainer item={item}>
							<div className="p-4 text-center">
								<div className="flex items-center justify-center gap-2">
									<DotsLoader size="sm" />
									<div className="text-sm text-muted-foreground">Loading tags...</div>
								</div>
							</div>
						</GridItemContainer>
					);
				}

				return (
					<GridItemContainer item={item}>
						{contact?.id ? (
							<TagSelector objectId={contact.id} objectType="contact" />
						) : (
							<div className="animate-pulse h-6 bg-muted rounded w-full" />
						)}
					</GridItemContainer>
				);
			default:
				return null;
		}
	};

	const renderRightColumn = (item: { id: string; title: string }) => {
		if (!activeOrganization) return null;
		
		switch (item.id) {
			case "summary":
				return (
					<GridItemContainer item={item}>
						<ContactSummary data={contact as any} organization={activeOrganization} />
					</GridItemContainer>
				);
			case "tasks":
				if (!loadedSections.has('tasks')) {
					return (
						<GridItemContainer item={item} icon={<IconPlus className="h-4 w-4 text-muted-foreground" />} onIconClick={() => {
							if (contact?.id) {
								const relatedObject = {
									id: contact.id,
									name: (contact as any).name || `${(contact as any).firstName || ''} ${(contact as any).lastName || ''}`.trim() || "Unnamed Contact",
									recordType: "contact" as const,
								};
								
								openCreateTask({
									defaultStatus: "todo",
									relatedObject,
								});
							}
						}}>
							<div className="p-4 text-center">
								<div className="flex items-center justify-center gap-2">
									<DotsLoader size="sm" />
									<div className="text-sm text-muted-foreground">Loading tasks...</div>
								</div>
							</div>
						</GridItemContainer>
					);
				}

				return (
					<GridItemContainer item={item} icon={<IconPlus className="h-4 w-4 text-muted-foreground" />} onIconClick={() => {
						if (contact?.id) {
							const relatedObject = {
								id: contact.id,
								name: (contact as any).name || `${(contact as any).firstName || ''} ${(contact as any).lastName || ''}`.trim() || "Unnamed Contact",
								recordType: "contact" as const,
							};
							
							openCreateTask({
								defaultStatus: "todo",
								relatedObject,
							});
						}
					}}>
						<ContactTasks 
							contact={contact as any}
							organization={activeOrganization} 
							user={user}
						/>
					</GridItemContainer>
				);
			case "portfolio":
				if (!loadedSections.has('portfolio')) {
					return (
						<GridItemContainer item={item}>
							<div className="p-4 text-center">
								<div className="flex items-center justify-center gap-2">
									<DotsLoader size="sm" />
									<div className="text-sm text-muted-foreground">Loading portfolio...</div>
								</div>
							</div>
						</GridItemContainer>
					);
				}

				return (
					<GridItemContainer item={item}>
						<Portfolio
							data={contact as any}
							organization={activeOrganization}
							properties={contactProperties as any || []}
							loading={isLoadingProperties}
						/>
					</GridItemContainer>
				);
			case "linked-contacts":
				if (!loadedSections.has('linked-contacts')) {
					return (
						<GridItemContainer item={item} icon={<IconPlus className="h-4 w-4 text-muted-foreground" />}>
							<div className="p-4 text-center">
								<div className="flex items-center justify-center gap-2">
									<DotsLoader size="sm" />
									<div className="text-sm text-muted-foreground">Loading linked contacts...</div>
								</div>
							</div>
						</GridItemContainer>
					);
				}

				return (
					<GridItemContainer item={item} icon={<IconPlus className="h-4 w-4 text-muted-foreground" />} >
						<LinkedContacts
							data={contact as any}
							organization={activeOrganization}
							linkedContacts={linkedContacts as any || []}
							loading={isLoadingLinkedContacts}
						/>
					</GridItemContainer>
				);
			case "metadata":
				return (
					<GridItemContainer item={item}>
						<ContactMetadata 
							data={contact as any} 
							organization={activeOrganization} 
						/>
					</GridItemContainer>
				);
			default:
				return null;
		}
	};

	const handleConfigurePage = () => {
		setIsConfigureModalOpen(true);
	};

	if (!activeOrganization) {
		return (
			<div className="flex items-center justify-center h-screen">
				<div className="text-muted-foreground">Loading...</div>
			</div>
		);
	}

	return (
		<div className="flex flex-col h-[calc(100vh-50px)]">
			{/* Header */}
			<PageHeader
				activeOrganization={activeOrganization}
				data={contact}
				isFavorite={isFavorite}
				objectType="contact"
				onTitleUpdate={() => {}}
				onConfigurePage={handleConfigurePage}
			/>

			{/* Body with three columns */}
			<div className="flex-1 flex overflow-hidden">
				{/* Left Column - Contact Details */}
				<div className="w-md border-r border-border overflow-y-auto">
					<ReorderList
						initialItems={leftColItems}
						renderItem={renderLeftColumn}
						storageKey="contact:left-column-order"
					/>
				</div>

				{/* Center Column - Activity Feed - LAZY LOAD */}
				<div className="flex-1 border-r border-border overflow-y-auto">
					{contact && activeOrganization && loadedSections.has('activities') ? (
						<Activities 
							data={contact}
							organization={activeOrganization}
							user={user}
							recordType="contact"
						/>
					) : (
						<div className="h-full flex items-center justify-center">
							<div className="text-center">
								<div className="flex items-center justify-center gap-2 mb-2">
									<DotsLoader size="sm" />
								</div>
								<div className="text-sm text-muted-foreground">Loading activity feed...</div>
							</div>
						</div>
					)}
				</div>

				{/* Right Column - Additional Information */}
				<div className="w-lg overflow-y-auto">
					<ReorderList
						initialItems={rightColItems}
						renderItem={renderRightColumn}
						storageKey="contact:right-column-order"
					/>
				</div>
			</div>

			{/* Configure Page Modal */}
			<ConfigurePageModal
				open={isConfigureModalOpen}
				onOpenChange={setIsConfigureModalOpen}
				objectType="contact"
			/>
		</div>
	);
};

export default ContactPage;