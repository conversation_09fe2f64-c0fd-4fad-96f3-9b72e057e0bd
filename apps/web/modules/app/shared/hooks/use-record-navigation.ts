import { useSearchParams } from "next/navigation";
import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";

export interface RecordNavigationInfo {
  currentIndex: number;
  totalRecords: number;
  filteredRecords: number;
  viewId?: string;
  viewName?: string;
  contextDescription?: string;
  hasPrevious: boolean;
  hasNext: boolean;
  navigateToPrevious: () => void;
  navigateToNext: () => void;
  isLoading: boolean;
}

interface NavigationResponse {
  previousId: string | null;
  nextId: string | null;
  currentIndex: number;
  totalRecords: number;
  filteredRecords: number;
  hasFilters: boolean;
  contextDescription: string;
  viewName?: string;
}

export function useRecordNavigation(
  objectType: string,
  currentId: string,
  organizationId: string
): RecordNavigationInfo {
  const searchParams = useSearchParams();
  const router = useRouter();

  // Build query parameters including filters and sorting but excluding pagination
  const buildQueryParams = useCallback(() => {
    const params = new URLSearchParams();
    params.set('objectType', objectType);
    params.set('currentId', currentId);
    params.set('organizationId', organizationId);

    // Add current filter and sort parameters from URL, but exclude pagination-related params
    const excludedParams = new Set(['start', 'size', 'cursor', 'direction', 'id', 'live']);
    
    searchParams.forEach((value, key) => {
      // Include search parameters that represent filters and sorting, but exclude pagination
      if (value && value.trim() && !excludedParams.has(key)) {
        params.set(key, value);
      }
    });
    
    return params;
  }, [objectType, currentId, organizationId, searchParams]);

  // Fetch navigation data from API
  const { data: navigationData, isLoading } = useQuery<NavigationResponse>({
    queryKey: ["recordNavigation", objectType, currentId, organizationId, searchParams.toString()],
    queryFn: async () => {
      const queryParams = buildQueryParams();
      const response = await fetch(`/api/navigation/record-context?${queryParams.toString()}`);

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to fetch record navigation data");
      }

      return response.json();
    },
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
    staleTime: 2 * 60 * 1000, // Consider data fresh for 2 minutes
    enabled: !!currentId && !!organizationId,
  });

  const hasPrevious = Boolean(navigationData?.previousId);
  const hasNext = Boolean(navigationData?.nextId);

  // Navigation functions
  const navigateToPrevious = useCallback(() => {
    if (!hasPrevious || !navigationData?.previousId) return;

    // Get the organization slug from the URL
    const orgSlug = window.location.pathname.split('/')[2];
    
    // Preserve current search params (filters, sorting, etc.) but navigate to new record
    const currentParams = searchParams.toString();
    const url = `/app/${orgSlug}/${objectType}/${navigationData.previousId}${currentParams ? `?${currentParams}` : ''}`;
    router.push(url);
  }, [hasPrevious, navigationData, searchParams, router, objectType]);

  const navigateToNext = useCallback(() => {
    if (!hasNext || !navigationData?.nextId) return;

    // Get the organization slug from the URL
    const orgSlug = window.location.pathname.split('/')[2];
    
    // Preserve current search params (filters, sorting, etc.) but navigate to new record
    const currentParams = searchParams.toString();
    const url = `/app/${orgSlug}/${objectType}/${navigationData.nextId}${currentParams ? `?${currentParams}` : ''}`;
    router.push(url);
  }, [hasNext, navigationData, searchParams, router, objectType]);

  return {
    currentIndex: navigationData?.currentIndex || 0,
    totalRecords: navigationData?.totalRecords || 0,
    filteredRecords: navigationData?.filteredRecords || 0,
    viewId: undefined, // We can add this back if needed from the search params
    viewName: navigationData?.viewName, // We can add this back if needed from API response
    contextDescription: navigationData?.contextDescription,
    hasPrevious,
    hasNext,
    navigateToPrevious,
    navigateToNext,
    isLoading,
  };
} 