import { Button } from "@ui/components/button";
import { IconChevronUp, IconChevronDown, IconLoader2 } from "@tabler/icons-react";
import { useRecordNavigation } from "../hooks/use-record-navigation";
import { cn } from "@ui/lib";
import { useEffect } from "react";

interface RecordNavigationProps {
  objectType: string;
  recordId: string;
  organizationId: string;
  className?: string;
}

export function RecordNavigation({ objectType, recordId, organizationId, className }: RecordNavigationProps) {
  const {
    currentIndex,
    totalRecords,
    filteredRecords,
    contextDescription,
    hasPrevious,
    hasNext,
    navigateToPrevious,
    navigateToNext,
    isLoading,
  } = useRecordNavigation(objectType, recordId, organizationId);

  // Add keyboard navigation
  useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      // Ignore if user is typing in an input or textarea
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      // Ignore if user is holding modifier keys
      if (event.ctrlKey || event.metaKey || event.altKey) {
        return;
      }

      switch (event.key.toLowerCase()) {
        case 'k':
          event.preventDefault();
          if (hasPrevious) {
            navigateToPrevious();
          }
          break;
        case 'j':
          event.preventDefault();
          if (hasNext) {
            navigateToNext();
          }
          break;
      }
    }

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [hasPrevious, hasNext, navigateToPrevious, navigateToNext]);

  if (isLoading) {
    return (
      <div className={cn("flex items-center gap-1", className)}>
        <div className="flex items-center gap-1 mr-2">
          <div className="flex flex-col items-center">
            <div className="text-sm font-medium text-foreground">
              <span className="inline-block h-4 w-4 bg-muted animate-pulse rounded font-mono -mb-1"></span>
              <span className="text-muted-foreground mx-1">of</span>
              <span className="inline-block h-4 w-8 bg-muted animate-pulse rounded font-mono -mb-1"></span>
              <span className="text-muted-foreground mx-1">in</span>
              <span className="inline-block h-4 w-20 bg-muted animate-pulse rounded max-w-32 -mb-1"></span>
            </div>
          </div>
        </div>
        <Button
          variant="relio"
          size="icon"
          className="h-8 w-8 p-0 text-muted-foreground opacity-50"
          disabled={true}
        >
          <IconChevronUp className="h-4 w-4" />
        </Button>
        
        <Button
          variant="relio"
          size="icon"
          className="h-8 w-8 p-0 text-muted-foreground opacity-50"
          disabled={true}
        >
          <IconChevronDown className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  if (totalRecords === 0) {
    return null;
  }

  const position = currentIndex + 1;
  const total = filteredRecords;

  return (
    <div className={cn("flex items-center gap-1", className)}>
      <div className="flex items-center gap-1 mr-2">
        <div className="flex flex-col items-center">
          <div className="text-sm font-medium text-foreground">
            <span className="font-mono">{position}</span>
            <span className="text-muted-foreground mx-1">of</span>
            <span className="font-mono">{total.toLocaleString()}</span>
            <span className="text-muted-foreground mx-1">in</span>
            <span className="text-muted-foreground truncate max-w-32 text-center" title={contextDescription}>{contextDescription}</span>
          </div>
        </div>
      </div>
      <Button
        variant="relio"
        size="icon"
        className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground disabled:opacity-50"
        onClick={navigateToPrevious}
        disabled={!hasPrevious}
        title="Previous record"
      >
        <IconChevronUp className="h-4 w-4" />
      </Button>
      
      <Button
        variant="relio"
        size="icon"
        className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground disabled:opacity-50"
        onClick={navigateToNext}
        disabled={!hasNext}
        title="Next record"
      >
        <IconChevronDown className="h-4 w-4" />
      </Button>
    </div>
  );
} 